'use client';

import { useState, useRef, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Upload, Download, RotateCcw, Type, Image as ImageIcon } from 'lucide-react';
import WatermarkCanvas from './watermark-canvas';
import { WatermarkCanvasRef, WatermarkSettings } from './types';

export default function WatermarkEditor() {
  const [image, setImage] = useState<HTMLImageElement | null>(null);
  const [watermarkSettings, setWatermarkSettings] = useState<WatermarkSettings>({
    type: 'text',
    text: '水印文字',
    fontSize: 48,
    fontFamily: 'Arial',
    color: '#ffffff',
    opacity: 0.7,
    position: 'bottom-right',
    offsetX: 20,
    offsetY: 20,
    rotation: 0,
    imageUrl: null,
    imageSize: 100,
  });

  const canvasRef = useRef<WatermarkCanvasRef>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const watermarkImageInputRef = useRef<HTMLInputElement>(null);

  const handleImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    if (!validTypes.includes(file.type)) {
      alert('不支持的文件格式。支持 JPG、PNG、WebP 和 GIF 格式。');
      return;
    }

    const img = new Image();
    const url = URL.createObjectURL(file);

    img.onload = () => {
      setImage(img);
      URL.revokeObjectURL(url);
    };

    img.onerror = () => {
      URL.revokeObjectURL(url);
      alert('图片加载失败，请检查文件是否损坏。');
    };

    img.src = url;
  }, []);

  const handleWatermarkImageUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validTypes = ['image/png', 'image/svg+xml'];
    if (!validTypes.includes(file.type)) {
      alert('水印图片仅支持 PNG 和 SVG 格式。');
      return;
    }

    const url = URL.createObjectURL(file);
    setWatermarkSettings(prev => ({
      ...prev,
      imageUrl: url
    }));
  }, []);

  const handleDownload = useCallback(async () => {
    if (!canvasRef.current || !image) return;
    
    try {
      await canvasRef.current.downloadImage('png');
    } catch (error) {
      console.error('下载失败:', error);
      alert('下载失败，请重试。');
    }
  }, [image]);

  const handleReset = useCallback(() => {
    setWatermarkSettings({
      type: 'text',
      text: '水印文字',
      fontSize: 48,
      fontFamily: 'Arial',
      color: '#ffffff',
      opacity: 0.7,
      position: 'bottom-right',
      offsetX: 20,
      offsetY: 20,
      rotation: 0,
      imageUrl: null,
      imageSize: 100,
    });
  }, []);

  const updateSettings = useCallback((updates: Partial<WatermarkSettings>) => {
    setWatermarkSettings(prev => ({ ...prev, ...updates }));
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50 dark:from-slate-900 dark:via-slate-900 dark:to-indigo-950/50 flex flex-col">
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />
      <input
        ref={watermarkImageInputRef}
        type="file"
        accept="image/png,image/svg+xml"
        onChange={handleWatermarkImageUpload}
        className="hidden"
      />

      {/* 工具栏 */}
      <div className="bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700 p-4">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-4">
            <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-200">水印工具</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <Upload className="w-4 h-4" />
              <span>上传图片</span>
            </Button>
            
            <Button
              onClick={handleReset}
              variant="outline"
              size="sm"
              disabled={!image}
              className="flex items-center space-x-2"
            >
              <RotateCcw className="w-4 h-4" />
              <span>重置</span>
            </Button>
            
            <Button
              onClick={handleDownload}
              disabled={!image}
              size="sm"
              className="flex items-center space-x-2"
            >
              <Download className="w-4 h-4" />
              <span>下载</span>
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex min-h-0">
        {/* 画布区域 */}
        <div className="flex-1 flex flex-col min-w-0">
          <div className="flex-1 overflow-auto p-8">
            <div className="min-h-full flex items-center justify-center">
              {image ? (
                <WatermarkCanvas
                  ref={canvasRef}
                  image={image}
                  watermarkSettings={watermarkSettings}
                  className="max-w-none shadow-2xl rounded-2xl overflow-hidden"
                />
              ) : (
                <div className="text-center">
                  <div className="w-64 h-64 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center mb-4">
                    <ImageIcon className="w-16 h-16 text-slate-400" />
                  </div>
                  <p className="text-slate-600 dark:text-slate-400 mb-4">请上传一张图片开始添加水印</p>
                  <Button onClick={() => fileInputRef.current?.click()}>
                    <Upload className="w-4 h-4 mr-2" />
                    选择图片
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 控制面板 */}
        <div className="w-80 bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm border-l border-slate-200 dark:border-slate-700 p-6 overflow-auto">
          <Tabs value={watermarkSettings.type} onValueChange={(value) => updateSettings({ type: value as 'text' | 'image' })}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="text" className="flex items-center space-x-2">
                <Type className="w-4 h-4" />
                <span>文字水印</span>
              </TabsTrigger>
              <TabsTrigger value="image" className="flex items-center space-x-2">
                <ImageIcon className="w-4 h-4" />
                <span>图片水印</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="text" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">文字设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="watermark-text">水印文字</Label>
                    <Input
                      id="watermark-text"
                      value={watermarkSettings.text}
                      onChange={(e) => updateSettings({ text: e.target.value })}
                      placeholder="输入水印文字"
                    />
                  </div>

                  <div>
                    <Label>字体大小: {watermarkSettings.fontSize}px</Label>
                    <Slider
                      value={[watermarkSettings.fontSize]}
                      onValueChange={([value]) => updateSettings({ fontSize: value })}
                      min={12}
                      max={200}
                      step={1}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="font-family">字体</Label>
                    <Select value={watermarkSettings.fontFamily} onValueChange={(value) => updateSettings({ fontFamily: value })}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Arial">Arial</SelectItem>
                        <SelectItem value="Helvetica">Helvetica</SelectItem>
                        <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                        <SelectItem value="Georgia">Georgia</SelectItem>
                        <SelectItem value="Verdana">Verdana</SelectItem>
                        <SelectItem value="SimSun">宋体</SelectItem>
                        <SelectItem value="SimHei">黑体</SelectItem>
                        <SelectItem value="Microsoft YaHei">微软雅黑</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="text-color">文字颜色</Label>
                    <Input
                      id="text-color"
                      type="color"
                      value={watermarkSettings.color}
                      onChange={(e) => updateSettings({ color: e.target.value })}
                      className="h-10"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="image" className="space-y-6 mt-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">图片设置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label>水印图片</Label>
                    <Button
                      onClick={() => watermarkImageInputRef.current?.click()}
                      variant="outline"
                      className="w-full mt-2"
                    >
                      <Upload className="w-4 h-4 mr-2" />
                      选择水印图片
                    </Button>
                    {watermarkSettings.imageUrl && (
                      <p className="text-sm text-green-600 mt-2">✓ 已选择水印图片</p>
                    )}
                  </div>

                  <div>
                    <Label>图片大小: {watermarkSettings.imageSize}%</Label>
                    <Slider
                      value={[watermarkSettings.imageSize]}
                      onValueChange={([value]) => updateSettings({ imageSize: value })}
                      min={10}
                      max={200}
                      step={5}
                      className="mt-2"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* 通用设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">位置和样式</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="position">水印位置</Label>
                <Select value={watermarkSettings.position} onValueChange={(value) => updateSettings({ position: value as any })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="top-left">左上角</SelectItem>
                    <SelectItem value="top-center">顶部居中</SelectItem>
                    <SelectItem value="top-right">右上角</SelectItem>
                    <SelectItem value="center-left">左侧居中</SelectItem>
                    <SelectItem value="center">居中</SelectItem>
                    <SelectItem value="center-right">右侧居中</SelectItem>
                    <SelectItem value="bottom-left">左下角</SelectItem>
                    <SelectItem value="bottom-center">底部居中</SelectItem>
                    <SelectItem value="bottom-right">右下角</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="offset-x">水平偏移</Label>
                  <Input
                    id="offset-x"
                    type="number"
                    value={watermarkSettings.offsetX}
                    onChange={(e) => updateSettings({ offsetX: parseInt(e.target.value) || 0 })}
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="offset-y">垂直偏移</Label>
                  <Input
                    id="offset-y"
                    type="number"
                    value={watermarkSettings.offsetY}
                    onChange={(e) => updateSettings({ offsetY: parseInt(e.target.value) || 0 })}
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label>透明度: {Math.round(watermarkSettings.opacity * 100)}%</Label>
                <Slider
                  value={[watermarkSettings.opacity]}
                  onValueChange={([value]) => updateSettings({ opacity: value })}
                  min={0.1}
                  max={1}
                  step={0.1}
                  className="mt-2"
                />
              </div>

              <div>
                <Label>旋转角度: {watermarkSettings.rotation}°</Label>
                <Slider
                  value={[watermarkSettings.rotation]}
                  onValueChange={([value]) => updateSettings({ rotation: value })}
                  min={-180}
                  max={180}
                  step={1}
                  className="mt-2"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
