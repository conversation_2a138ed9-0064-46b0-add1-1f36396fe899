export interface WatermarkSettings {
  type: 'text' | 'image';
  
  // 文字水印设置
  text: string;
  fontSize: number;
  fontFamily: string;
  color: string;
  
  // 图片水印设置
  imageUrl: string | null;
  imageSize: number; // 百分比
  
  // 通用设置
  opacity: number; // 0-1
  position: 'top-left' | 'top-center' | 'top-right' | 'center-left' | 'center' | 'center-right' | 'bottom-left' | 'bottom-center' | 'bottom-right' | 'custom';
  offsetX: number; // 像素偏移
  offsetY: number; // 像素偏移
  rotation: number; // 旋转角度 -180 到 180
}

export interface WatermarkCanvasRef {
  getCanvas: () => HTMLCanvasElement | null;
  downloadImage: (format: 'png' | 'jpeg', quality?: number) => Promise<void>;
}

export interface WatermarkPosition {
  x: number;
  y: number;
}
