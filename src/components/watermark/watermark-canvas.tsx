'use client';

import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from 'react';
import { WatermarkSettings, WatermarkCanvasRef, WatermarkPosition } from './types';

interface WatermarkCanvasProps {
  image: HTMLImageElement | null;
  watermarkSettings: WatermarkSettings;
  className?: string;
}

const WatermarkCanvas = forwardRef<WatermarkCanvasRef, WatermarkCanvasProps>(
  ({ image, watermarkSettings, className = '' }, ref) => {
    const canvasRef = useRef<HTMLCanvasElement>(null);
    const [watermarkImage, setWatermarkImage] = useState<HTMLImageElement | null>(null);

    useImperativeHandle(ref, () => ({
      getCanvas: () => canvasRef.current,
      downloadImage: async (format: 'png' | 'jpeg', quality = 0.9) => {
        const canvas = canvasRef.current;
        if (!canvas || !image) return;

        try {
          // 创建高分辨率导出画布
          const exportCanvas = createExportCanvas(image, watermarkSettings, watermarkImage);
          
          const mimeType = format === 'png' ? 'image/png' : 'image/jpeg';
          
          // 创建下载链接
          exportCanvas.toBlob((blob) => {
            if (!blob) return;
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = `watermarked-image.${format}`;
            link.href = url;
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            URL.revokeObjectURL(url);
          }, mimeType, quality);
        } catch (error) {
          console.error('导出失败:', error);
          throw error;
        }
      },
    }));

    // 加载水印图片
    useEffect(() => {
      if (watermarkSettings.type === 'image' && watermarkSettings.imageUrl) {
        const img = new Image();
        img.onload = () => setWatermarkImage(img);
        img.onerror = () => {
          console.error('水印图片加载失败');
          setWatermarkImage(null);
        };
        img.src = watermarkSettings.imageUrl;
      } else {
        setWatermarkImage(null);
      }
    }, [watermarkSettings.imageUrl, watermarkSettings.type]);

    // 绘制画布
    useEffect(() => {
      if (!image) return;
      
      const canvas = canvasRef.current;
      const ctx = canvas?.getContext('2d');
      if (!canvas || !ctx) return;

      // 计算显示尺寸
      const maxWidth = 800;
      const maxHeight = 600;
      
      let { width, height } = image;
      
      // 按比例缩放以适应显示区域
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // 清空画布
      ctx.clearRect(0, 0, width, height);

      // 绘制原图
      ctx.drawImage(image, 0, 0, width, height);

      // 绘制水印
      drawWatermark(ctx, width, height, watermarkSettings, watermarkImage);
    }, [image, watermarkSettings, watermarkImage]);

    return (
      <div className={`bg-gray-50 dark:bg-gray-900 rounded-lg ${className}`}>
        <canvas
          ref={canvasRef}
          className="border border-gray-200 dark:border-gray-700 rounded shadow-lg"
          style={{ display: 'block' }}
        />
      </div>
    );
  }
);

WatermarkCanvas.displayName = 'WatermarkCanvas';

// 计算水印位置
function calculateWatermarkPosition(
  canvasWidth: number,
  canvasHeight: number,
  watermarkWidth: number,
  watermarkHeight: number,
  settings: WatermarkSettings
): WatermarkPosition {
  let x = 0;
  let y = 0;

  switch (settings.position) {
    case 'top-left':
      x = settings.offsetX;
      y = settings.offsetY;
      break;
    case 'top-center':
      x = (canvasWidth - watermarkWidth) / 2 + settings.offsetX;
      y = settings.offsetY;
      break;
    case 'top-right':
      x = canvasWidth - watermarkWidth - settings.offsetX;
      y = settings.offsetY;
      break;
    case 'center-left':
      x = settings.offsetX;
      y = (canvasHeight - watermarkHeight) / 2 + settings.offsetY;
      break;
    case 'center':
      x = (canvasWidth - watermarkWidth) / 2 + settings.offsetX;
      y = (canvasHeight - watermarkHeight) / 2 + settings.offsetY;
      break;
    case 'center-right':
      x = canvasWidth - watermarkWidth - settings.offsetX;
      y = (canvasHeight - watermarkHeight) / 2 + settings.offsetY;
      break;
    case 'bottom-left':
      x = settings.offsetX;
      y = canvasHeight - watermarkHeight - settings.offsetY;
      break;
    case 'bottom-center':
      x = (canvasWidth - watermarkWidth) / 2 + settings.offsetX;
      y = canvasHeight - watermarkHeight - settings.offsetY;
      break;
    case 'bottom-right':
      x = canvasWidth - watermarkWidth - settings.offsetX;
      y = canvasHeight - watermarkHeight - settings.offsetY;
      break;
    case 'custom':
      x = settings.offsetX;
      y = settings.offsetY;
      break;
  }

  return { x, y };
}

// 绘制水印
function drawWatermark(
  ctx: CanvasRenderingContext2D,
  canvasWidth: number,
  canvasHeight: number,
  settings: WatermarkSettings,
  watermarkImage: HTMLImageElement | null
) {
  ctx.save();
  
  // 设置透明度
  ctx.globalAlpha = settings.opacity;

  if (settings.type === 'text') {
    // 绘制文字水印
    ctx.font = `${settings.fontSize}px ${settings.fontFamily}`;
    ctx.fillStyle = settings.color;
    ctx.textBaseline = 'top';

    // 测量文字尺寸
    const textMetrics = ctx.measureText(settings.text);
    const textWidth = textMetrics.width;
    const textHeight = settings.fontSize;

    // 计算位置
    const position = calculateWatermarkPosition(
      canvasWidth,
      canvasHeight,
      textWidth,
      textHeight,
      settings
    );

    // 应用旋转
    if (settings.rotation !== 0) {
      ctx.translate(position.x + textWidth / 2, position.y + textHeight / 2);
      ctx.rotate((settings.rotation * Math.PI) / 180);
      ctx.translate(-textWidth / 2, -textHeight / 2);
      ctx.fillText(settings.text, 0, 0);
    } else {
      ctx.fillText(settings.text, position.x, position.y);
    }
  } else if (settings.type === 'image' && watermarkImage) {
    // 绘制图片水印
    const originalWidth = watermarkImage.width;
    const originalHeight = watermarkImage.height;
    
    // 计算缩放后的尺寸
    const scale = settings.imageSize / 100;
    const scaledWidth = originalWidth * scale;
    const scaledHeight = originalHeight * scale;

    // 计算位置
    const position = calculateWatermarkPosition(
      canvasWidth,
      canvasHeight,
      scaledWidth,
      scaledHeight,
      settings
    );

    // 应用旋转
    if (settings.rotation !== 0) {
      ctx.translate(position.x + scaledWidth / 2, position.y + scaledHeight / 2);
      ctx.rotate((settings.rotation * Math.PI) / 180);
      ctx.drawImage(watermarkImage, -scaledWidth / 2, -scaledHeight / 2, scaledWidth, scaledHeight);
    } else {
      ctx.drawImage(watermarkImage, position.x, position.y, scaledWidth, scaledHeight);
    }
  }

  ctx.restore();
}

// 创建导出用的高分辨率画布
function createExportCanvas(
  image: HTMLImageElement,
  settings: WatermarkSettings,
  watermarkImage: HTMLImageElement | null
): HTMLCanvasElement {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;

  // 使用原图尺寸
  canvas.width = image.width;
  canvas.height = image.height;

  // 绘制原图
  ctx.drawImage(image, 0, 0);

  // 绘制水印
  drawWatermark(ctx, canvas.width, canvas.height, settings, watermarkImage);

  return canvas;
}

export default WatermarkCanvas;
