"use client"

import React, { useEffect, useMemo, useState } from "react"
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'

// 批量处理相关类型
interface BatchFile {
  file: File
  id: string
  imageUrl: string
  imgSize: { w: number; h: number } | null
  previewSlices: Array<{ url: string; width: number; height: number }>
  status: 'pending' | 'processing' | 'completed' | 'error'
  error?: string
}

interface BatchProgress {
  current: number
  total: number
  currentFileName: string
}

export default function ImageSplitter() {
  // 批量处理模式
  const [batchMode, setBatchMode] = useState(false)
  const [files, setFiles] = useState<BatchFile[]>([])
  const [batchProgress, setBatchProgress] = useState<BatchProgress | null>(null)

  // 单文件模式（保持向后兼容）
  const [file, setFile] = useState<File | null>(null)
  const [mode, setMode] = useState<"count" | "grid">("count")
  const [count, setCount] = useState<number>(9) // 总块数 n
  const [rows, setRows] = useState<number>(3)
  const [cols, setCols] = useState<number>(3)
  const [format, setFormat] = useState<"image/png" | "image/jpeg">("image/png")
  const [quality, setQuality] = useState<number>(0.92) // 仅 jpeg 生效
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // 预览相关
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [imgSize, setImgSize] = useState<{ w: number; h: number } | null>(null)
  const [previewing, setPreviewing] = useState(false)
  const [previewSlices, setPreviewSlices] = useState<Array<{ url: string; width: number; height: number }>>([]);
  const [showGrid, setShowGrid] = useState(true)
  // 自定义尺寸相关
  const [useCustomSize, setUseCustomSize] = useState(false)
  const [customWidth, setCustomWidth] = useState<number | "">(200)
  const [customHeight, setCustomHeight] = useState<number | "">(200)
  const [lockAspectRatio, setLockAspectRatio] = useState(true)
  // 智能命名规则相关
  const [useCustomNaming, setUseCustomNaming] = useState(false)
  const [namingTemplate, setNamingTemplate] = useState("slice_{index}_{row}x{col}")
  const [includeOriginalName, setIncludeOriginalName] = useState(true)


  const computedGrid = useMemo(() => {
    if (mode === "grid") return { rows, cols }
    const n = Math.max(1, Math.floor(count || 1))
    const r = Math.floor(Math.sqrt(n))
    const c = Math.ceil(n / r)
    return { rows: r, cols: c }
  }, [mode, count, rows, cols])

  // 叠加网格线（以百分比定位，避免依赖实际像素）
  const gridLines = useMemo(() => {
    const hs = Array.from({ length: Math.max(0, computedGrid.rows - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.rows)
    const vs = Array.from({ length: Math.max(0, computedGrid.cols - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.cols)
    return { hs, vs }
  }, [computedGrid])

  // 根据文件生成对象 URL，并计算原图尺寸
  useEffect(() => {
    if (!file) {
      setImageUrl(null)
      setImgSize(null)
      setPreviewSlices([])
      setPreviewing(false)
      return
    }

    // 立即清空之前的预览
    setPreviewSlices([])
    setPreviewing(true)

    const url = URL.createObjectURL(file)
    setImageUrl(url)
    let cancelled = false
    ;(async () => {
      try {
        const img = await loadImage(url)
        if (!cancelled) {
          setImgSize({ w: img.width, h: img.height })
          // 自动生成切片预览
          generatePreviewSlices(img)
        }
      } catch (e) {
        console.error(e)
        if (!cancelled) {
          setPreviewing(false)
        }
      }
    })()
    return () => {
      cancelled = true
      URL.revokeObjectURL(url)
    }
  }, [file])

  // 当网格参数改变时重新生成预览
  useEffect(() => {
    if (imageUrl && imgSize && !previewing) {
      // 立即清空旧预览并显示加载状态
      setPreviewSlices([])
      setPreviewing(true)

      loadImage(imageUrl).then(img => {
        generatePreviewSlices(img)
      }).catch(error => {
        console.error('重新生成预览失败:', error)
        setPreviewing(false)
      })
    }
  }, [computedGrid, imageUrl, imgSize])

  // 生成切片预览
  const generatePreviewSlices = async (img: HTMLImageElement) => {
    setPreviewing(true)
    setPreviewSlices([]) // 立即清空旧预览

    try {
      // 使用 setTimeout 让 UI 有时间更新
      await new Promise(resolve => setTimeout(resolve, 100))

      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        setPreviewing(false)
        return
      }

      const sliceWidth = Math.floor(img.width / computedGrid.cols)
      const sliceHeight = Math.floor(img.height / computedGrid.rows)

      canvas.width = sliceWidth
      canvas.height = sliceHeight

      const newSlices: Array<{ url: string; width: number; height: number }> = []

      for (let row = 0; row < computedGrid.rows; row++) {
        for (let col = 0; col < computedGrid.cols; col++) {
          const x = col * sliceWidth
          const y = row * sliceHeight

          // 清空画布
          ctx.clearRect(0, 0, sliceWidth, sliceHeight)

          // 绘制切片
          ctx.drawImage(
            img,
            x, y, sliceWidth, sliceHeight,
            0, 0, sliceWidth, sliceHeight
          )

          // 转换为 blob URL
          const dataUrl = canvas.toDataURL('image/png', 0.8) // 降低质量提升速度
          newSlices.push({
            url: dataUrl,
            width: sliceWidth,
            height: sliceHeight
          })

          // 每处理几个切片就让出控制权，避免阻塞 UI
          if (newSlices.length % 4 === 0) {
            await new Promise(resolve => setTimeout(resolve, 10))
          }
        }
      }

      setPreviewSlices(newSlices)
      setPreviewing(false)
    } catch (error) {
      console.error('生成预览失败:', error)
      setPreviewing(false)
      setPreviewSlices([])
    }
  }



  // 批量文件处理函数
  const handleBatchFileUpload = (selectedFiles: FileList | File[]) => {
    const fileArray = Array.from(selectedFiles)
    const imageFiles = fileArray.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length === 0) {
      setError("请选择至少一张图片文件")
      return
    }

    const batchFiles: BatchFile[] = imageFiles.map(file => ({
      file,
      id: `${file.name}-${Date.now()}-${Math.random()}`,
      imageUrl: URL.createObjectURL(file),
      imgSize: null,
      previewSlices: [],
      status: 'pending'
    }))

    setFiles(batchFiles)
    setBatchMode(true)
    setError(null)

    // 异步加载每个图片的尺寸信息
    batchFiles.forEach(async (batchFile) => {
      try {
        const img = await loadImage(batchFile.imageUrl)
        setFiles(prev => prev.map(f =>
          f.id === batchFile.id
            ? { ...f, imgSize: { w: img.width, h: img.height } }
            : f
        ))
      } catch (error) {
        console.error(`Failed to load image ${batchFile.file.name}:`, error)
        setFiles(prev => prev.map(f =>
          f.id === batchFile.id
            ? { ...f, status: 'error', error: '图片加载失败' }
            : f
        ))
      }
    })
  }

  // 移除批量文件
  const removeBatchFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.imageUrl)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 清空批量文件
  const clearBatchFiles = () => {
    files.forEach(f => URL.revokeObjectURL(f.imageUrl))
    setFiles([])
    setBatchMode(false)
    setBatchProgress(null)
  }

  return (
    <div className="h-screen flex flex-col bg-background">

      <div className="flex-1 flex min-h-0">
        {/* 左侧：控制面板 */}
        <div className="w-[480px] flex-shrink-0 border-r bg-card overflow-y-auto">
          <div className="p-6 space-y-6">
            {/* 标题区域 */}
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">图片切分工具</h1>
              <p className="text-muted-foreground">将图片按网格切分为多个小图片</p>
            </div>

            {/* 文件上传卡片 */}
            <Card className="border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors">
              <CardContent className="p-6">
                {/* 模式切换 */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-sm">
                      <span className="text-primary-foreground text-lg">⚡</span>
                    </div>
                    <div>
                      <Label className="font-semibold text-base">批量处理</Label>
                      <p className="text-sm text-muted-foreground">同时处理多张图片</p>
                    </div>
                  </div>
                  <Switch
                    checked={batchMode}
                    onCheckedChange={(checked) => {
                      setBatchMode(checked)
                      if (!checked) {
                        clearBatchFiles()
                      } else {
                        setFile(null)
                        setImageUrl(null)
                        setImgSize(null)
                        setPreviewSlices([])
                      }
                    }}
                  />
                </div>

                {/* 文件上传区域 */}
                <div className="space-y-4">
                  <div className="text-center py-8 px-4 bg-gradient-to-br from-muted/30 to-muted/10 rounded-xl border border-muted-foreground/20">
                    <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-3xl">📁</span>
                    </div>
                    <h3 className="font-semibold text-lg mb-2">
                      {batchMode ? "选择多张图片" : "选择图片文件"}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-6">
                      {batchMode ? "支持多张图片批量处理" : "支持单张图片处理"}
                    </p>

                    <Input
                      id="file"
                      type="file"
                      accept="image/png,image/jpeg,image/webp"
                      multiple={batchMode}
                      onChange={(e) => {
                        if (batchMode) {
                          if (e.target.files && e.target.files.length > 0) {
                            handleBatchFileUpload(e.target.files)
                          }
                        } else {
                          setFile(e.target.files?.[0] || null)
                        }
                      }}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      size="lg"
                      className="px-8"
                      onClick={() => document.getElementById('file')?.click()}
                    >
                      <span className="mr-2">📂</span>
                      选择文件
                    </Button>

                    <div className="flex justify-center gap-2 mt-4">
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">PNG</span>
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">JPEG</span>
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">WebP</span>
                    </div>
                  </div>

                {/* 单文件模式显示 */}
                {!batchMode && file && (
                  <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                        <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-green-800 dark:text-green-200 truncate">{file.name}</p>
                        <p className="text-sm text-green-600 dark:text-green-400">{formatBytes(file.size)}</p>
                      </div>
                    </div>
                  </div>
                )}

                {/* 批量模式显示 */}
                {batchMode && files.length > 0 && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                          <span className="text-blue-600 dark:text-blue-400 text-sm">📊</span>
                        </div>
                        <div>
                          <p className="font-medium text-blue-800 dark:text-blue-200">
                            已选择 {files.length} 张图片
                          </p>
                          <p className="text-sm text-blue-600 dark:text-blue-400">准备批量处理</p>
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearBatchFiles}
                        className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900"
                      >
                        清空
                      </Button>
                    </div>
                    <div className="max-h-40 overflow-y-auto space-y-2 border rounded-lg p-2 bg-muted/20">
                      {files.map((batchFile) => (
                        <div key={batchFile.id} className="flex items-center justify-between p-3 bg-background rounded-lg border shadow-sm">
                          <div className="flex items-center gap-3 flex-1 min-w-0">
                            <div className={`w-3 h-3 rounded-full ${
                              batchFile.status === 'completed' ? 'bg-green-500' :
                              batchFile.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                              batchFile.status === 'error' ? 'bg-red-500' :
                              'bg-gray-400'
                            }`} />
                            <span className="truncate text-sm font-medium">{batchFile.file.name}</span>
                            <Badge variant="secondary" className="text-xs">
                              {formatBytes(batchFile.file.size)}
                            </Badge>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeBatchFile(batchFile.id)}
                            className="h-8 w-8 p-0 hover:text-destructive hover:bg-destructive/10"
                          >
                            ×
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {error && (
                  <div className="rounded-md border border-destructive/20 bg-destructive/10 text-destructive text-sm px-3 py-2">
                    {error}
                  </div>
                )}
                </div>
              </CardContent>
            </Card>

            {/* 参数设置卡片 */}
            <Card className="border-primary/20">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm">
                    <span className="text-white text-lg">⚙️</span>
                  </div>
                  <div>
                    <CardTitle className="text-xl">参数设置</CardTitle>
                    <CardDescription>配置切分模式和输出格式</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 拆分模式选择 */}
                <div className="space-y-3">
                  <Label className="font-medium">拆分模式</Label>
                  <RadioGroup
                    className="grid grid-cols-1 gap-3"
                    value={mode}
                    onValueChange={(v) => setMode(v as any)}
                  >
                    <div className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors data-[state=checked]:border-primary data-[state=checked]:bg-primary/5">
                      <RadioGroupItem id="mode-count" value="count" />
                      <div className="flex-1">
                        <Label htmlFor="mode-count" className="cursor-pointer font-medium">按总数量</Label>
                        <p className="text-sm text-muted-foreground">自动计算接近方形的行列数</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors data-[state=checked]:border-primary data-[state=checked]:bg-primary/5">
                      <RadioGroupItem id="mode-grid" value="grid" />
                      <div className="flex-1">
                        <Label htmlFor="mode-grid" className="cursor-pointer font-medium">自定义网格</Label>
                        <p className="text-sm text-muted-foreground">手动指定行数和列数</p>
                      </div>
                    </div>
                  </RadioGroup>
                </div>

                {/* 动态参数输入区域 */}
                <div className="bg-muted/50 rounded-lg p-4 border">
                  {mode === "count" ? (
                    <div className="space-y-3">
                      <Label htmlFor="count" className="font-medium">总块数</Label>
                      <div className="flex items-center gap-4">
                        <Input
                          id="count"
                          className="w-20 text-center font-semibold"
                          type="number"
                          min={1}
                          value={count}
                          onChange={(e) => setCount(parseInt(e.target.value || "1", 10))}
                        />
                        <div className="flex-1">
                          <div className="font-medium">
                            自动计算为：{computedGrid.rows} × {computedGrid.cols}
                          </div>
                          <p className="text-sm text-muted-foreground">系统会自动计算接近方形的行列数</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <Label className="font-medium">网格尺寸</Label>
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <div className="space-y-1">
                            <Label htmlFor="rows" className="text-xs text-muted-foreground">行数</Label>
                            <Input
                              id="rows"
                              className="w-16 text-center font-semibold"
                              type="number"
                              min={1}
                              value={rows}
                              onChange={(e) => setRows(parseInt(e.target.value || "1", 10))}
                            />
                          </div>
                          <span className="text-xl text-muted-foreground mt-5">×</span>
                          <div className="space-y-1">
                            <Label htmlFor="cols" className="text-xs text-muted-foreground">列数</Label>
                            <Input
                              id="cols"
                              className="w-16 text-center font-semibold"
                              type="number"
                              min={1}
                              value={cols}
                              onChange={(e) => setCols(parseInt(e.target.value || "1", 10))}
                            />
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="font-medium">
                            总计：{rows * cols} 张切片
                          </div>
                          <p className="text-sm text-muted-foreground">手动指定行数和列数</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <Label className="font-medium">显示网格线</Label>
                  <Switch checked={showGrid} onCheckedChange={setShowGrid} />
                </div>

                <div className="flex gap-4 items-end">
                  <div className="space-y-2 flex-1">
                    <Label className="font-medium">导出格式</Label>
                    <Select value={format} onValueChange={(v) => setFormat(v as any)}>
                      <SelectTrigger>
                        <SelectValue placeholder="选择格式" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="image/png">PNG（无损）</SelectItem>
                        <SelectItem value="image/jpeg">JPEG（有损）</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  {format === "image/jpeg" && (
                    <div className="space-y-2 flex-1">
                      <Label htmlFor="quality" className="font-medium">JPEG 质量（0-1）</Label>
                      <Input
                        id="quality"
                        type="number"
                        min={0}
                        max={1}
                        step={0.01}
                        value={quality}
                        onChange={(e) => setQuality(Number(e.target.value))}
                      />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* 操作按钮 */}
            <Card className="border-green-200 dark:border-green-800 bg-gradient-to-br from-green-50/50 to-emerald-50/50 dark:from-green-950/20 dark:to-emerald-950/20">
              <CardContent className="pt-6">
                <Button
                  onClick={() => {/* TODO: 实现切分功能 */}}
                  disabled={loading || (!batchMode && !file) || (batchMode && files.length === 0)}
                  size="lg"
                  className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg"
                >
                  <span className="mr-2">✂️</span>
                  {loading ? "处理中..." : batchMode ? "批量切分并下载" : "切分并下载"}
                </Button>
                {batchProgress && (
                  <div className="mt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>处理进度</span>
                      <span>{batchProgress.current}/{batchProgress.total}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
                      />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      正在处理：{batchProgress.currentFileName}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 中间：原图预览区域 */}
        <div className="w-[520px] flex-shrink-0 flex flex-col border-r">
          <div className="border-b bg-card px-6 py-4">
            <h2 className="text-lg font-semibold">
              {batchMode ? "批量文件预览" : "原图预览"}
            </h2>
            <p className="text-sm text-muted-foreground">
              {batchMode ? "查看所有待处理的图片" : "实时预览切分效果"}
            </p>
          </div>
          <div className="flex-1 p-6 overflow-auto bg-muted/20">
            {batchMode ? (
              // 批量模式预览
              <div className="space-y-4">
                {files.length === 0 ? (
                  <div className="flex items-center justify-center h-full text-center py-20">
                    <div>
                      <div className="text-6xl mb-4">📁</div>
                      <h3 className="text-lg font-medium mb-2">请选择图片文件</h3>
                      <p className="text-muted-foreground">支持批量上传多张图片</p>
                    </div>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-3">
                    {files.map((batchFile) => (
                      <div key={batchFile.id} className="relative group">
                        <div className="h-32 bg-card border rounded-lg overflow-hidden flex items-center justify-center p-2">
                          <div className="relative inline-block max-w-full max-h-full">
                            <img
                              src={batchFile.imageUrl}
                              alt={batchFile.file.name}
                              className="max-w-full max-h-full object-contain"
                            />
                            {showGrid && batchFile.imgSize && (
                              <div className="pointer-events-none absolute inset-0">
                                {/* 横线 */}
                                {gridLines.hs.map((p, i) => (
                                  <div
                                    key={`h-${i}`}
                                    className="absolute w-full border-t-2 border-red-500/80"
                                    style={{ top: `${p}%` }}
                                  />
                                ))}
                                {/* 竖线 */}
                                {gridLines.vs.map((p, i) => (
                                  <div
                                    key={`v-${i}`}
                                    className="absolute h-full border-l-2 border-red-500/80"
                                    style={{ left: `${p}%` }}
                                  />
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="mt-2 text-sm">
                          <div className="font-medium truncate">{batchFile.file.name}</div>
                          {batchFile.imgSize && (
                            <div className="text-muted-foreground">
                              {batchFile.imgSize.w} × {batchFile.imgSize.h} px → {computedGrid.rows}×{computedGrid.cols} 切片
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ) : (
              // 单文件模式预览
              <div className="h-full flex items-center justify-center">
                {!file ? (
                  <div className="text-center">
                    <div className="text-6xl mb-4">🖼️</div>
                    <h3 className="text-lg font-medium mb-2">请选择图片文件</h3>
                    <p className="text-muted-foreground">支持 PNG、JPEG、WebP 格式</p>
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center p-3">
                    <div className="relative inline-block">
                      <img
                        src={imageUrl || ""}
                        alt="原图预览"
                        className="max-w-full max-h-full object-contain rounded-lg border bg-card shadow-lg"
                        style={{ maxHeight: '50vh', maxWidth: '100%' }}
                      />
                      {/* 网格线叠加 - 只覆盖图片区域 */}
                      {showGrid && imageUrl && (
                        <div className="absolute inset-0 pointer-events-none">
                          {/* 水平线 */}
                          {gridLines.hs.map((top, i) => (
                            <div
                              key={`h-${i}`}
                              className="absolute w-full border-t-2 border-red-500/80"
                              style={{ top: `${top}%` }}
                            />
                          ))}
                          {/* 垂直线 */}
                          {gridLines.vs.map((left, i) => (
                            <div
                              key={`v-${i}`}
                              className="absolute h-full border-l-2 border-red-500/80"
                              style={{ left: `${left}%` }}
                            />
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* 图片信息栏 */}
          {!batchMode && imgSize && (
            <div className="border-t bg-card px-6 py-3">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-6">
                  <span>原图尺寸：{imgSize.w} × {imgSize.h} px</span>
                  <span>切分网格：{computedGrid.rows} × {computedGrid.cols}</span>
                  <span>预计生成：{computedGrid.rows * computedGrid.cols} 张切片</span>
                </div>
                {useCustomSize && typeof customWidth === "number" && typeof customHeight === "number" && (
                  <span>输出尺寸：{customWidth} × {customHeight} px</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 右侧：切片预览区域 */}
        <div className="flex-1 bg-card flex flex-col">
          <div className="border-b px-6 py-4">
            <h3 className="font-semibold">切片预览</h3>
            {!previewing && previewSlices.length > 0 && (
              <p className="text-sm text-muted-foreground">
                {previewSlices.length} 张切片 · {computedGrid.rows} × {computedGrid.cols} 网格
              </p>
            )}
          </div>
          <div className="flex-1 p-6 overflow-y-auto">
            {previewing && (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                  <div className="text-lg font-medium mb-2">正在生成切片预览</div>
                  <div className="text-sm text-muted-foreground mb-2">
                    {computedGrid.rows} × {computedGrid.cols} = {computedGrid.rows * computedGrid.cols} 个切片
                  </div>
                  <div className="text-xs text-muted-foreground">
                    大图片可能需要更长时间处理...
                  </div>
                </div>
              </div>
            )}
            {!previewing && previewSlices.length === 0 && (
              <div className="flex items-center justify-center h-full text-center">
                <div>
                  <div className="text-4xl mb-3">🔲</div>
                  <div className="text-sm text-muted-foreground">暂无预览</div>
                  <div className="text-xs text-muted-foreground mt-1">请先选择图片</div>
                </div>
              </div>
            )}
            {!previewing && previewSlices.length > 0 && (
              <div className="grid grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                {previewSlices.map((slice, index) => (
                  <div key={index} className="relative group">
                    <div className="aspect-square bg-muted/20 border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                      <img
                        src={slice.url}
                        alt={`切片 ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="absolute top-1 left-1 bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded font-medium">
                      #{index + 1}
                    </div>
                    <div className="absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
                      {slice.width}×{slice.height}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

function loadImage(url: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}