"use client"

import { useState, useEffect, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"

type SplitMode = "count" | "grid"

interface BatchFile {
  id: string
  file: File
  imageUrl: string
  imgSize: { w: number; h: number } | null
  status: 'pending' | 'processing' | 'completed' | 'error'
}

interface BatchProgress {
  current: number
  total: number
  currentFileName: string
}

export default function ImageSplitter() {
  // 基础状态
  const [mode, setMode] = useState<SplitMode>("grid")
  const [count, setCount] = useState<number>(9)
  const [rows, setRows] = useState<number>(3)
  const [cols, setCols] = useState<number>(3)
  const [format, setFormat] = useState<"image/png" | "image/jpeg">("image/png")
  const [quality, setQuality] = useState<number>(0.92)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // 预览相关
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [imgSize, setImgSize] = useState<{ w: number; h: number } | null>(null)
  const [previewing, setPreviewing] = useState(false)
  const [previewSlices, setPreviewSlices] = useState<Array<{ url: string; width: number; height: number }>>([])
  const [showGrid, setShowGrid] = useState(true)
  
  // 文件相关
  const [file, setFile] = useState<File | null>(null)
  const [batchMode, setBatchMode] = useState(false)
  const [files, setFiles] = useState<BatchFile[]>([])
  const [batchProgress, setBatchProgress] = useState<BatchProgress | null>(null)

  const computedGrid = useMemo(() => {
    if (mode === "grid") return { rows, cols }
    const sqrt = Math.sqrt(count)
    const r = Math.floor(sqrt)
    const c = Math.ceil(count / r)
    return { rows: r, cols: c }
  }, [mode, count, rows, cols])

  const gridLines = useMemo(() => {
    const hs = Array.from({ length: computedGrid.rows - 1 }, (_, i) => ((i + 1) / computedGrid.rows) * 100)
    const vs = Array.from({ length: computedGrid.cols - 1 }, (_, i) => ((i + 1) / computedGrid.cols) * 100)
    return { hs, vs }
  }, [computedGrid])

  // 格式化文件大小
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 批量文件处理函数
  const handleBatchFileUpload = (selectedFiles: FileList | File[]) => {
    const fileArray = Array.from(selectedFiles)
    const newFiles: BatchFile[] = fileArray.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file,
      imageUrl: URL.createObjectURL(file),
      imgSize: null,
      status: 'pending'
    }))
    
    setFiles(prev => [...prev, ...newFiles])
    
    // 异步加载图片尺寸
    newFiles.forEach(async (batchFile) => {
      try {
        const img = await loadImage(batchFile.imageUrl)
        setFiles(prev => prev.map(f => 
          f.id === batchFile.id 
            ? { ...f, imgSize: { w: img.width, h: img.height } }
            : f
        ))
      } catch (error) {
        console.error('Failed to load image:', error)
      }
    })
  }

  // 移除批量文件
  const removeBatchFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id)
      if (fileToRemove) {
        URL.revokeObjectURL(fileToRemove.imageUrl)
      }
      return prev.filter(f => f.id !== id)
    })
  }

  // 清空批量文件
  const clearBatchFiles = () => {
    files.forEach(f => URL.revokeObjectURL(f.imageUrl))
    setFiles([])
    setBatchMode(false)
    setBatchProgress(null)
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      <div className="flex-1 flex min-h-0">
        {/* 左侧：控制面板 */}
        <div className="w-[480px] flex-shrink-0 border-r bg-card overflow-y-auto">
          <div className="p-6 space-y-6">
            {/* 标题区域 */}
            <div className="space-y-2">
              <h1 className="text-2xl font-bold tracking-tight">图片切分工具</h1>
              <p className="text-muted-foreground">将图片按网格切分为多个小图片</p>
            </div>

            {/* 文件上传卡片 */}
            <Card className="border-2 border-dashed border-muted-foreground/25 hover:border-primary/50 transition-colors">
              <CardContent className="p-6">
                {/* 模式切换 */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-lg flex items-center justify-center shadow-sm">
                      <span className="text-primary-foreground text-lg">⚡</span>
                    </div>
                    <div>
                      <Label className="font-semibold text-base">批量处理</Label>
                      <p className="text-sm text-muted-foreground">同时处理多张图片</p>
                    </div>
                  </div>
                  <Switch
                    checked={batchMode}
                    onCheckedChange={(checked) => {
                      setBatchMode(checked)
                      if (!checked) {
                        clearBatchFiles()
                      } else {
                        setFile(null)
                        setImageUrl(null)
                        setImgSize(null)
                        setPreviewSlices([])
                      }
                    }}
                  />
                </div>

                {/* 文件上传区域 */}
                <div className="space-y-4">
                  <div className="text-center py-8 px-4 bg-gradient-to-br from-muted/30 to-muted/10 rounded-xl border border-muted-foreground/20">
                    <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-3xl">📁</span>
                    </div>
                    <h3 className="font-semibold text-lg mb-2">
                      {batchMode ? "选择多张图片" : "选择图片文件"}
                    </h3>
                    <p className="text-sm text-muted-foreground mb-6">
                      {batchMode ? "支持多张图片批量处理" : "支持单张图片处理"}
                    </p>
                    
                    <Input
                      id="file"
                      type="file"
                      accept="image/png,image/jpeg,image/webp"
                      multiple={batchMode}
                      onChange={(e) => {
                        if (batchMode) {
                          if (e.target.files && e.target.files.length > 0) {
                            handleBatchFileUpload(e.target.files)
                          }
                        } else {
                          setFile(e.target.files?.[0] || null)
                        }
                      }}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      size="lg"
                      className="px-8"
                      onClick={() => document.getElementById('file')?.click()}
                    >
                      <span className="mr-2">📂</span>
                      选择文件
                    </Button>
                    
                    <div className="flex justify-center gap-2 mt-4">
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">PNG</span>
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">JPEG</span>
                      <span className="px-3 py-1 bg-background/80 rounded-full text-xs font-medium border">WebP</span>
                    </div>
                  </div>

                  {/* 单文件模式显示 */}
                  {!batchMode && file && (
                    <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                          <span className="text-green-600 dark:text-green-400 text-sm">✓</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-green-800 dark:text-green-200 truncate">{file.name}</p>
                          <p className="text-sm text-green-600 dark:text-green-400">{formatBytes(file.size)}</p>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 批量模式显示 */}
                  {batchMode && files.length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                            <span className="text-blue-600 dark:text-blue-400 text-sm">📊</span>
                          </div>
                          <div>
                            <p className="font-medium text-blue-800 dark:text-blue-200">
                              已选择 {files.length} 张图片
                            </p>
                            <p className="text-sm text-blue-600 dark:text-blue-400">准备批量处理</p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={clearBatchFiles}
                          className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-700 dark:text-blue-300 dark:hover:bg-blue-900"
                        >
                          清空
                        </Button>
                      </div>
                      <div className="max-h-40 overflow-y-auto space-y-2 border rounded-lg p-2 bg-muted/20">
                        {files.map((batchFile) => (
                          <div key={batchFile.id} className="flex items-center justify-between p-3 bg-background rounded-lg border shadow-sm">
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                              <div className={`w-3 h-3 rounded-full ${
                                batchFile.status === 'completed' ? 'bg-green-500' :
                                batchFile.status === 'processing' ? 'bg-blue-500 animate-pulse' :
                                batchFile.status === 'error' ? 'bg-red-500' :
                                'bg-gray-400'
                              }`} />
                              <span className="truncate text-sm font-medium">{batchFile.file.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                {formatBytes(batchFile.file.size)}
                              </Badge>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeBatchFile(batchFile.id)}
                              className="h-8 w-8 p-0 hover:text-destructive hover:bg-destructive/10"
                            >
                              ×
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {error && (
                    <div className="rounded-md border border-destructive/20 bg-destructive/10 text-destructive text-sm px-3 py-2">
                      {error}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

function loadImage(url: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}
